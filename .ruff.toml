line-length = 120

[lint]
select = [
    "E", "F", "W", "I", "N", "UP", "B", "C4", "SIM", "RET", "ARG", "PTH", "PL", "RUF"
]

ignore = [
    "E501", "E402", "F403", "F405",
    "PLR0913", "PLR0915", "PLR0912", "PLR2004",  # config-heavy patterns
    "PLR0911", "PLR1702",                        # nested logic in action scripts
    "N806",                                      # variable naming flexibility
    "UP007",                                     # Union syntax for Python 3.9
    "F841",                                      # unused variables (often intentional)
    "F401",                                      # unused imports (often for type checking)
    "ARG001", "ARG002",                          # unused function arguments (callbacks, interfaces)
    "RET505", "RET506", "RET507",                # unnecessary else/elif after return
    "SIM102", "SIM108",                          # simplify conditionals (sometimes less readable)
    "B008",                                      # function calls in argument defaults
    "C901",                                      # complex functions (genealogy logic is inherently complex)
    "PLW2901",                                   # redefined loop variables
    "RUF012",                                    # mutable class attributes
]

[lint.isort]
combine-as-imports = true
force-single-line = false
known-first-party = ["core", "config"]
known-third-party = [
  "selenium", "requests", "sqlalchemy", "bs4", "cloudscraper", "dotenv", "tabulate", "tqdm"
]

[format]
quote-style = "preserve"
indent-style = "space"
line-ending = "auto"
skip-magic-trailing-comma = false
